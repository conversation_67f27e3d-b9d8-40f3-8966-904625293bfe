import { ipc<PERSON>ain, dialog, app, BrowserWindow } from 'electron'
import databaseService from './database'
import fileSystemService from './fileSystem'
import fileWatcherService from './fileWatcher'
import { IPC_CHANNELS } from '../shared/ipcTypes'
import type {
  CreateProjectRequest,
  UpdateProjectRequest,
  CreateAreaRequest,
  CreateTaskRequest,
  UpdateTaskRequest,
  CreateResourceRequest,
  CreateProjectKPIRequest,
  UpdateProjectKPIRequest,
  CreateDeliverableRequest,
  UpdateDeliverableRequest,
  LinkResourceToProjectRequest,
  CreateDocumentLinkRequest,
  UpdateDocumentLinkRequest,
  UpdateDocumentPathRequest,
  SearchLinksRequest,
  ReplaceDocumentLinksRequest,
  ReadFileRequest,
  WriteFileRequest,
  MoveFileRequest,
  CopyFileRequest
} from '../shared/ipcTypes'

class IpcHandler {
  private isInitialized = false

  /**
   * Initialize IPC handlers
   */
  initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.setupDatabaseHandlers()
    this.setupFileSystemHandlers()
    this.setupAppHandlers()
    this.setupWindowHandlers()
    this.setupEventForwarding()

    this.isInitialized = true
    console.log('IPC handlers initialized')
  }

  /**
   * Setup database operation handlers
   */
  private setupDatabaseHandlers(): void {
    // Database initialization
    ipcMain.handle(IPC_CHANNELS.DB_INITIALIZE, async () => {
      try {
        return await databaseService.initialize()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Database initialization failed'
        }
      }
    })

    // Test database connection
    ipcMain.handle(IPC_CHANNELS.DB_TEST_CONNECTION, async () => {
      try {
        return await databaseService.testConnection()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Connection test failed'
        }
      }
    })

    // Project operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT, async (_, data: CreateProjectRequest) => {
      try {
        return await databaseService.createProject(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECTS, async () => {
      try {
        return await databaseService.getProjects()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get projects'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT, async (_, data: UpdateProjectRequest) => {
      try {
        const client = databaseService.getClient()
        const result = await client.project.update({
          where: { id: data.id },
          data: data.updates
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT, async (_, id: string) => {
      try {
        const client = databaseService.getClient()
        await client.project.delete({
          where: { id }
        })
        return {
          success: true
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete project'
        }
      }
    })

    // ProjectKPI operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_PROJECT_KPI, async (_, data: CreateProjectKPIRequest) => {
      try {
        return await databaseService.createProjectKPI(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create project KPI'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_KPIS, async (_, projectId: string) => {
      try {
        return await databaseService.getProjectKPIs(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project KPIs'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_PROJECT_KPI, async (_, data: UpdateProjectKPIRequest) => {
      try {
        return await databaseService.updateProjectKPI(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update project KPI'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_PROJECT_KPI, async (_, id: string) => {
      try {
        return await databaseService.deleteProjectKPI(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete project KPI'
        }
      }
    })

    // Deliverable operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_DELIVERABLE, async (_, data: CreateDeliverableRequest) => {
      try {
        return await databaseService.createDeliverable(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create deliverable'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_DELIVERABLES, async (_, projectId: string) => {
      try {
        return await databaseService.getProjectDeliverables(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project deliverables'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_DELIVERABLE, async (_, data: UpdateDeliverableRequest) => {
      try {
        return await databaseService.updateDeliverable(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update deliverable'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DELIVERABLE, async (_, id: string) => {
      try {
        return await databaseService.deleteDeliverable(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete deliverable'
        }
      }
    })

    // Resource linking operations
    ipcMain.handle(IPC_CHANNELS.DB_GET_PROJECT_RESOURCES, async (_, projectId: string) => {
      try {
        return await databaseService.getProjectResources(projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get project resources'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_LINK_RESOURCE_TO_PROJECT, async (_, data: LinkResourceToProjectRequest) => {
      try {
        return await databaseService.linkResourceToProject(data)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to link resource to project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_PROJECT, async (_, resourceId: string, projectId: string) => {
      try {
        return await databaseService.unlinkResourceFromProject(resourceId, projectId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unlink resource from project'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREA_RESOURCES, async (_, areaId: string) => {
      try {
        return await databaseService.getAreaResources(areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get area resources'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UNLINK_RESOURCE_FROM_AREA, async (_, resourceId: string, areaId: string) => {
      try {
        return await databaseService.unlinkResourceFromArea(resourceId, areaId)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unlink resource from area'
        }
      }
    })

    // Area operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_AREA, async (_, data: CreateAreaRequest) => {
      try {
        const client = databaseService.getClient()
        // {{ AURA-X: Modify - 支持完整的Area字段. Approval: 寸止(ID:1738157400). }}
        const result = await client.area.create({
          data: {
            name: data.name,
            description: data.description || null,
            color: data.color || null,
            icon: data.icon || null,
            standard: data.standard || null,
            status: data.status || 'Active',
            reviewFrequency: data.reviewFrequency || 'Weekly',
            archived: data.archived || false
          }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create area'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_AREAS, async () => {
      try {
        const client = databaseService.getClient()
        const result = await client.area.findMany({
          where: { archived: false },
          orderBy: { createdAt: 'desc' }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get areas'
        }
      }
    })

    // Task operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_TASK, async (_, data: CreateTaskRequest) => {
      try {
        console.log('Creating task with data:', {
          content: data.content,
          projectId: data.projectId,
          areaId: data.areaId
        })

        const client = databaseService.getClient()

        // 验证外键约束 - 只有当ID存在且有效时才设置
        const taskData: any = {
          content: data.content,
          description: data.description,
          priority: data.priority,
          deadline: data.deadline,
          sourceText: data.sourceText,
          sourceContext: data.sourceContext
        }

        // 只有当projectId有效时才设置
        if (data.projectId && data.projectId !== 'none') {
          // 验证项目是否存在
          const project = await client.project.findUnique({
            where: { id: data.projectId }
          })
          if (project) {
            taskData.projectId = data.projectId
            console.log('Task assigned to project:', project.name)
          } else {
            console.warn('Project not found:', data.projectId)
            // 项目不存在时，任务将保持未分配状态
          }
        }

        // 只有当areaId有效时才设置
        if (data.areaId && data.areaId !== 'none') {
          // 验证领域是否存在
          const area = await client.area.findUnique({
            where: { id: data.areaId }
          })
          if (area) {
            taskData.areaId = data.areaId
            console.log('Task assigned to area:', area.name)
          } else {
            console.warn('Area not found:', data.areaId)
            // 领域不存在时，任务将保持未分配状态
          }
        }

        // 只有当parentId有效时才设置
        if (data.parentId) {
          // 验证父任务是否存在
          const parentTask = await client.task.findUnique({
            where: { id: data.parentId }
          })
          if (parentTask) {
            taskData.parentId = data.parentId
          }
        }

        // 只有当sourceResourceId有效时才设置
        if (data.sourceResourceId) {
          // 验证资源是否存在
          const resource = await client.resourceLink.findUnique({
            where: { id: data.sourceResourceId }
          })
          if (resource) {
            taskData.sourceResourceId = data.sourceResourceId
          }
        }

        const result = await client.task.create({
          data: taskData
        })

        console.log('Task created successfully:', {
          id: result.id,
          content: result.content,
          projectId: result.projectId,
          areaId: result.areaId
        })

        return {
          success: true,
          data: result
        }
      } catch (error) {
        console.error('Task creation error:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create task'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_TASKS, async (_, filters?: any) => {
      try {
        const client = databaseService.getClient()
        const where: any = {}

        if (filters?.projectId) {
          where.projectId = filters.projectId
        }
        if (filters?.areaId) {
          where.areaId = filters.areaId
        }
        if (filters?.completed !== undefined) {
          where.completed = filters.completed
        }

        const result = await client.task.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          include: {
            tags: {
              include: {
                tag: true
              }
            }
          }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get tasks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_UPDATE_TASK, async (_, data: UpdateTaskRequest) => {
      try {
        const client = databaseService.getClient()
        const result = await client.task.update({
          where: { id: data.id },
          data: data.updates
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update task'
        }
      }
    })

    // Resource operations
    ipcMain.handle(IPC_CHANNELS.DB_CREATE_RESOURCE, async (_, data: CreateResourceRequest) => {
      try {
        const client = databaseService.getClient()

        // Validate that the referenced entities exist
        if (data.projectId) {
          const project = await client.project.findUnique({
            where: { id: data.projectId }
          })
          if (!project) {
            throw new Error(`Project with ID ${data.projectId} does not exist`)
          }
        }

        if (data.areaId) {
          const area = await client.area.findUnique({
            where: { id: data.areaId }
          })
          if (!area) {
            throw new Error(`Area with ID ${data.areaId} does not exist`)
          }
        }

        const result = await client.resourceLink.create({
          data: {
            resourcePath: data.resourcePath,
            title: data.title,
            projectId: data.projectId || null,
            areaId: data.areaId || null
          }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create resource'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_RESOURCES, async (_, filters?: any) => {
      try {
        const client = databaseService.getClient()
        const where: any = {}

        if (filters?.projectId) {
          where.projectId = filters.projectId
        }
        if (filters?.areaId) {
          where.areaId = filters.areaId
        }

        const result = await client.resourceLink.findMany({
          where,
          orderBy: { id: 'desc' }
        })
        return {
          success: true,
          data: result
        }
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get resources'
        }
      }
    })

    // Document Link operations
    ipcMain.handle(
      IPC_CHANNELS.DB_CREATE_DOCUMENT_LINK,
      async (_, data: CreateDocumentLinkRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.createLink(data)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to create document link'
          }
        }
      }
    )

    ipcMain.handle(IPC_CHANNELS.DB_GET_DOCUMENT_LINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getDocumentLinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get document links'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_BACKLINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getBacklinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get backlinks'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_OUTLINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getOutlinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get outlinks'
        }
      }
    })

    ipcMain.handle(
      IPC_CHANNELS.DB_UPDATE_DOCUMENT_LINK,
      async (_, data: UpdateDocumentLinkRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.updateLink(data)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to update document link'
          }
        }
      }
    )

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINK, async (_, id: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.deleteLink(id)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete document link'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_DELETE_DOCUMENT_LINKS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.deleteDocumentLinks(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete document links'
        }
      }
    })

    ipcMain.handle(
      IPC_CHANNELS.DB_UPDATE_DOCUMENT_PATH,
      async (_, data: UpdateDocumentPathRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.updateDocumentPath(data.oldPath, data.newPath, data.newTitle)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to update document path'
          }
        }
      }
    )

    ipcMain.handle(IPC_CHANNELS.DB_MARK_LINKS_INVALID, async (_, targetDocPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.markLinksAsInvalid(targetDocPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to mark links as invalid'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_MARK_LINKS_VALID, async (_, targetDocPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.markLinksAsValid(targetDocPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to mark links as valid'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_GET_LINK_STATISTICS, async (_, docPath: string) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.getLinkStatistics(docPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get link statistics'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.DB_SEARCH_LINKS, async (_, data: SearchLinksRequest) => {
      try {
        const linkService = databaseService.getDocumentLinkService()
        return await linkService.searchLinks(data.query, data.filters)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to search links'
        }
      }
    })

    ipcMain.handle(
      IPC_CHANNELS.DB_REPLACE_DOCUMENT_LINKS,
      async (_, data: ReplaceDocumentLinksRequest) => {
        try {
          const linkService = databaseService.getDocumentLinkService()
          return await linkService.replaceDocumentLinks(data.sourceDocPath, data.links)
        } catch (error) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to replace document links'
          }
        }
      }
    )
  }

  /**
   * Setup file system operation handlers
   */
  private setupFileSystemHandlers(): void {
    // File system initialization
    ipcMain.handle(IPC_CHANNELS.FS_INITIALIZE, async () => {
      try {
        return await fileSystemService.initialize()
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'File system initialization failed'
        }
      }
    })

    // File system re-initialization with custom workspace
    ipcMain.handle(IPC_CHANNELS.FS_REINITIALIZE, async (_, workspaceDirectory: string) => {
      try {
        return await fileSystemService.initialize(workspaceDirectory)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'File system re-initialization failed'
        }
      }
    })

    // File operations
    ipcMain.handle(IPC_CHANNELS.FS_READ_FILE, async (_, data: ReadFileRequest) => {
      try {
        return await fileSystemService.readFile(data.path, { encoding: data.encoding })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to read file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_WRITE_FILE, async (_, data: WriteFileRequest) => {
      try {
        return await fileSystemService.writeFile(data.path, data.content, {
          encoding: data.encoding,
          createDirs: data.createDirs,
          backup: data.backup
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to write file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_DELETE_FILE, async (_, path: string) => {
      try {
        return await fileSystemService.deleteFile(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_MOVE_FILE, async (_, data: MoveFileRequest) => {
      try {
        return await fileSystemService.moveFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to move file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_COPY_FILE, async (_, data: CopyFileRequest) => {
      try {
        return await fileSystemService.copyFile(data.sourcePath, data.targetPath, {
          overwrite: data.overwrite,
          createDirs: data.createDirs
        })
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to copy file'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_FILE_EXISTS, async (_, path: string) => {
      try {
        return await fileSystemService.fileExists(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to check file existence'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_GET_FILE_INFO, async (_, path: string) => {
      try {
        return await fileSystemService.getFileInfo(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get file info'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_LIST_DIRECTORY, async (_, path: string) => {
      try {
        return await fileSystemService.listDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to list directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_CREATE_DIRECTORY, async (_, path: string) => {
      try {
        return await fileSystemService.createDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create directory'
        }
      }
    })

    // File system delete directory
    ipcMain.handle(IPC_CHANNELS.FS_DELETE_DIRECTORY, async (_, path: string) => {
      try {
        return await fileSystemService.deleteDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete directory'
        }
      }
    })

    // File system rename
    ipcMain.handle(IPC_CHANNELS.FS_RENAME, async (_, oldPath: string, newPath: string) => {
      try {
        return await fileSystemService.rename(oldPath, newPath)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to rename file or directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_WATCH_DIRECTORY, async (_, path: string) => {
      try {
        return await fileWatcherService.watchDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to watch directory'
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.FS_UNWATCH_DIRECTORY, async (_, path: string) => {
      try {
        return await fileWatcherService.unwatchDirectory(path)
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to unwatch directory'
        }
      }
    })
  }

  /**
   * Setup application operation handlers
   */
  private setupAppHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.APP_GET_VERSION, async () => {
      return app.getVersion()
    })

    ipcMain.handle(IPC_CHANNELS.APP_GET_PATH, async (_, name: string) => {
      return app.getPath(name as any)
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_MESSAGE_BOX, async (_, options: any) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showMessageBox(focusedWindow, options)
      } else {
        return await dialog.showMessageBox(options)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_ERROR_BOX, async (_, title: string, content: string) => {
      dialog.showErrorBox(title, content)
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_OPEN_DIALOG, async (_, options: any) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showOpenDialog(focusedWindow, options)
      } else {
        return await dialog.showOpenDialog(options)
      }
    })

    ipcMain.handle(IPC_CHANNELS.APP_SHOW_SAVE_DIALOG, async (_, options: any) => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        return await dialog.showSaveDialog(focusedWindow, options)
      } else {
        return await dialog.showSaveDialog(options)
      }
    })
  }

  /**
   * Setup window operation handlers
   */
  private setupWindowHandlers(): void {
    ipcMain.handle(IPC_CHANNELS.WINDOW_MINIMIZE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.minimize()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_MAXIMIZE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        if (focusedWindow.isMaximized()) {
          focusedWindow.unmaximize()
        } else {
          focusedWindow.maximize()
        }
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_CLOSE, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.close()
      }
    })

    ipcMain.handle(IPC_CHANNELS.WINDOW_TOGGLE_DEVTOOLS, async () => {
      const focusedWindow = BrowserWindow.getFocusedWindow()
      if (focusedWindow) {
        focusedWindow.webContents.toggleDevTools()
      }
    })
  }

  /**
   * Setup event forwarding from services to renderer
   */
  private setupEventForwarding(): void {
    // Forward file system events to all renderer processes
    fileWatcherService.on('file-system-event', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_EVENT, event)
      })
    })

    fileWatcherService.on('file-created', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_FILE_CREATED, event)
      })
    })

    fileWatcherService.on('file-changed', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_FILE_CHANGED, event)
      })
    })

    fileWatcherService.on('file-deleted', (event) => {
      BrowserWindow.getAllWindows().forEach((window) => {
        window.webContents.send(IPC_CHANNELS.FS_FILE_DELETED, event)
      })
    })
  }

  /**
   * Remove all IPC handlers
   */
  cleanup(): void {
    if (!this.isInitialized) {
      return
    }

    // Remove all handlers
    Object.values(IPC_CHANNELS).forEach((channel) => {
      ipcMain.removeAllListeners(channel)
    })

    this.isInitialized = false
    console.log('IPC handlers cleaned up')
  }
}

// Export singleton instance
export const ipcHandler = new IpcHandler()
export default ipcHandler
