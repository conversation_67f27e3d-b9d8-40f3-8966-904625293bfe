// schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  username  String   @unique
  password  String   // Hashed password
  settings  Json?    // 用户设置，如主题偏好、资源库路径等
}

model Project {
  id              String    @id @default(cuid())
  name            String
  description     String?
  status          String    @default("Not Started") // "Not Started", "In Progress", "At Risk", "Paused", "Completed"
  progress        Int       @default(0)
  goal            String?   // 项目目标，一句话描述项目"为什么"存在
  deliverable     String?   // 最终成果，可以是链接到资源、可量化指标或checklist
  startDate       DateTime?
  deadline        DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  archived        Boolean   @default(false)

  areaId          String?
  area            Area?     @relation(fields: [areaId], references: [id])
  tasks           Task[]
  resources       ResourceLink[]
  kpis            ProjectKPI[] // 项目关键绩效指标
  deliverables    Deliverable[] // 项目交付成果
}

model ProjectKPI {
  id          String    @id @default(cuid())
  name        String    // 指标名称，如"Bug修复数"
  value       String    // 当前值，存为字符串以支持不同类型
  target      String?   // 目标值
  unit        String?   // 单位，如"个"、"%"
  frequency   String?   // 记录频率：daily, weekly, monthly
  updatedAt   DateTime  @default(now())

  projectId   String
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  records     KPIRecord[] // KPI历史记录
}

model KPIRecord {
  id          String    @id @default(cuid())
  value       String    // 记录的数值
  note        String?   // 可选备注
  recordedAt  DateTime  @default(now())

  kpiId       String
  kpi         ProjectKPI @relation(fields: [kpiId], references: [id], onDelete: Cascade)

  @@index([kpiId, recordedAt])
}

model Deliverable {
  id          String    @id @default(cuid())
  title       String    // 成果标题
  description String?   // 成果描述
  type        String    @default("document") // document, milestone, metric, artifact
  status      String    @default("planned") // planned, in_progress, completed, delivered, accepted
  priority    String?   // low, medium, high, critical

  // 时间管理
  plannedDate DateTime? // 计划完成日期
  actualDate  DateTime? // 实际完成日期
  deadline    DateTime? // 截止日期

  // 成果内容
  content     String?   // 成果内容或描述
  attachments Json?     // 附件信息（文件路径等）
  metrics     Json?     // 量化指标（如果是metric类型）

  // 关联关系
  projectId   String
  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // 关联任务
  tasks       Task[]    // 相关任务

  // 关联资源
  resources   DeliverableResource[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model DeliverableResource {
  id            String      @id @default(cuid())
  deliverableId String
  deliverable   Deliverable @relation(fields: [deliverableId], references: [id], onDelete: Cascade)
  resourcePath  String      // 资源文件路径
  resourceType  String      // file, link, document
  title         String?     // 资源标题
  description   String?     // 资源描述

  createdAt     DateTime    @default(now())
}

model Area {
  id              String    @id @default(cuid())
  name            String
  standard        String?   // 领域标准，定义该领域的"成功状态"
  description     String?
  icon            String?
  color           String?   // 颜色代码
  status          String    @default("Active")  // 状态：Active, Inactive, On Hold
  reviewFrequency String    @default("Weekly")  // 审查频率：Daily, Weekly, Monthly
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  archived        Boolean   @default(false)

  projects        Project[]
  resources       ResourceLink[]
  habits          Habit[]       // 习惯追踪
  metrics         AreaMetric[]  // 领域关键指标
  tasks           Task[]        // 直接关联到领域的任务（如重复性任务）
  checklists      Checklist[]   // 领域标准核查清单
}

model AreaMetric {
  id          String    @id @default(cuid())
  name        String    // 指标名称，如"体重"
  value       String    // 当前值，存为字符串以支持不同类型
  unit        String?   // 单位，如"kg"
  updatedAt   DateTime  @default(now())
  
  areaId      String
  area        Area      @relation(fields: [areaId], references: [id], onDelete: Cascade)
}

model Habit {
  id          String    @id @default(cuid())
  name        String
  frequency   String    // "daily", "weekly", "monthly"等
  target      Int       // 目标次数，如"每周5次"
  createdAt   DateTime  @default(now())
  
  areaId      String
  area        Area      @relation(fields: [areaId], references: [id], onDelete: Cascade)
  records     HabitRecord[]
}

model HabitRecord {
  id          String    @id @default(cuid())
  date        DateTime
  completed   Boolean   @default(true)
  
  habitId     String
  habit       Habit     @relation(fields: [habitId], references: [id], onDelete: Cascade)

  @@unique([habitId, date]) // 确保每个习惯每天只有一条记录
}

model Checklist {
  id          String    @id @default(cuid())
  name        String
  template    Json      // 存储清单模板项
  createdAt   DateTime  @default(now())
  
  areaId      String
  area        Area      @relation(fields: [areaId], references: [id], onDelete: Cascade)
  instances   ChecklistInstance[]
}

model ChecklistInstance {
  id          String    @id @default(cuid())
  status      Json      // 存储每个项目的完成状态
  createdAt   DateTime  @default(now())
  completedAt DateTime?
  
  checklistId String
  checklist   Checklist @relation(fields: [checklistId], references: [id], onDelete: Cascade)
}

model Task {
  id          String    @id @default(cuid())
  content     String
  description String?
  completed   Boolean   @default(false)
  priority    String?   // "low", "medium", "high", "critical"
  deadline    DateTime?

  // 时间管理
  estimatedHours    Float?    // 预估工时（小时）
  actualHours       Float?    // 实际工时（小时）
  startedAt         DateTime? // 开始时间
  completedAt       DateTime? // 完成时间

  // 任务状态和进度
  status            String?   @default("todo") // "todo", "in_progress", "blocked", "review", "done"
  progress          Int?      @default(0) // 进度百分比 0-100

  // 任务依赖
  blockedBy         String?   // 阻塞原因描述
  dependencies      Json?     // 依赖的任务ID列表，存储为JSON数组

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 支持任务层级结构
  parentId    String?
  parent      Task?     @relation("TaskHierarchy", fields: [parentId], references: [id])
  subtasks    Task[]    @relation("TaskHierarchy")

  // 支持重复任务
  repeatRule  String?   // 如 "daily", "weekly:1,3,5"(周一三五), "monthly:15"(每月15号)

  // 关联
  projectId   String?
  project     Project?  @relation(fields: [projectId], references: [id])

  areaId      String?   // 允许任务直接关联到领域（用于重复性任务）
  area        Area?     @relation(fields: [areaId], references: [id])

  deliverableId String?   // 关联到交付成果
  deliverable   Deliverable? @relation(fields: [deliverableId], references: [id])

  // 资源关联 - 支持任务链接回原始资源
  sourceResourceId String?
  sourceResource   ResourceLink? @relation(fields: [sourceResourceId], references: [id])
  sourceText       String?       // 创建任务时选中的原始文本
  sourceContext    String?       // 原始文本的上下文（前后几行）

  tags        TaskTag[]
}

model Tag {
  id          String    @id @default(cuid())
  name        String    @unique
  color       String?
  icon        String?
  
  tasks       TaskTag[]
}

model TaskTag {
  taskId      String
  tagId       String
  
  task        Task      @relation(fields: [taskId], references: [id], onDelete: Cascade)
  tag         Tag       @relation(fields: [tagId], references: [id], onDelete: Cascade)
  
  @@id([taskId, tagId])
}

// 资源链接表，用于多对多关系
model ResourceLink {
  id            String  @id @default(cuid())
  resourcePath  String  // 绝对路径到.md文件
  title         String? // 资源标题，用于显示

  projectId     String?
  project       Project? @relation(fields: [projectId], references: [id])

  areaId        String?
  area          Area?    @relation(fields: [areaId], references: [id])

  // 反向关联 - 从此资源创建的任务
  createdTasks  Task[]
}

model Review {
  id          String    @id @default(cuid())
  type        String    // "Weekly", "Monthly", "Quarterly", "Yearly"
  period      String    // e.g., "2025-W28", "2025-07"
  content     Json      // 存储编辑器的结构化内容
  createdAt   DateTime  @default(now())
  completedAt DateTime?
}

// 收件箱笔记
model InboxNote {
  id          String    @id @default(cuid())
  title       String?
  content     String    // Markdown内容
  isDaily     Boolean   @default(false) // 是否为每日笔记
  processed   Boolean   @default(false) // 是否已处理
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// 文档双向链接表
model DocumentLink {
  id              String    @id @default(cuid())

  // 源文档信息
  sourceDocPath   String    // 源文档的绝对路径
  sourceDocTitle  String?   // 源文档标题（缓存用于显示）

  // 目标文档信息
  targetDocPath   String    // 目标文档的绝对路径
  targetDocTitle  String?   // 目标文档标题（缓存用于显示）

  // 链接信息
  linkText        String    // 链接文本，如 "项目管理"
  displayText     String?   // 显示文本，如果使用了 [[target|display]] 语法
  linkType        String    @default("wikilink") // 链接类型：wikilink, reference, mention

  // 位置信息
  startPosition   Int       // 链接在源文档中的开始位置
  endPosition     Int       // 链接在源文档中的结束位置
  lineNumber      Int       // 链接所在行号
  columnNumber    Int       // 链接所在列号

  // 上下文信息
  contextBefore   String?   // 链接前的文本上下文（用于预览）
  contextAfter    String?   // 链接后的文本上下文（用于预览）

  // 状态信息
  isValid         Boolean   @default(true)  // 链接是否有效（目标文档是否存在）
  linkStrength    Float     @default(1.0)   // 链接强度（基于使用频率等因素）

  // 时间戳
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  lastValidated   DateTime  @default(now()) // 最后验证时间

  // 索引优化
  @@index([sourceDocPath])
  @@index([targetDocPath])
  @@index([sourceDocPath, targetDocPath])
  @@index([linkType])
  @@index([isValid])
}