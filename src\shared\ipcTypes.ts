// IPC communication types

import type {
  <PERSON><PERSON><PERSON>ationR<PERSON>ult,
  <PERSON>In<PERSON>,
  FileContent,
  FileSystemConfig,
  FileSystemEvent
} from './fileTypes'
import type { DatabaseResult, DatabaseConfig } from './types'

// IPC Channel names
export const IPC_CHANNELS = {
  // Database operations
  DB_INITIALIZE: 'db:initialize',
  DB_TEST_CONNECTION: 'db:test-connection',
  DB_CREATE_PROJECT: 'db:create-project',
  DB_GET_PROJECTS: 'db:get-projects',
  DB_UPDATE_PROJECT: 'db:update-project',
  DB_DELETE_PROJECT: 'db:delete-project',
  DB_CREATE_AREA: 'db:create-area',
  DB_GET_AREAS: 'db:get-areas',
  DB_CREATE_TASK: 'db:create-task',
  DB_GET_TASKS: 'db:get-tasks',
  DB_UPDATE_TASK: 'db:update-task',
  DB_CREATE_RESOURCE: 'db:create-resource',
  DB_GET_RESOURCES: 'db:get-resources',
  DB_GET_PROJECT_RESOURCES: 'db:get-project-resources',
  DB_LINK_RESOURCE_TO_PROJECT: 'db:link-resource-to-project',
  DB_UNLINK_RESOURCE_FROM_PROJECT: 'db:unlink-resource-from-project',
  DB_GET_AREA_RESOURCES: 'db:get-area-resources',
  DB_UNLINK_RESOURCE_FROM_AREA: 'db:unlink-resource-from-area',
  // ProjectKPI operations
  DB_CREATE_PROJECT_KPI: 'db:create-project-kpi',
  DB_GET_PROJECT_KPIS: 'db:get-project-kpis',
  DB_UPDATE_PROJECT_KPI: 'db:update-project-kpi',
  DB_DELETE_PROJECT_KPI: 'db:delete-project-kpi',

  // Deliverable operations
  DB_CREATE_DELIVERABLE: 'db:create-deliverable',
  DB_GET_PROJECT_DELIVERABLES: 'db:get-project-deliverables',
  DB_UPDATE_DELIVERABLE: 'db:update-deliverable',
  DB_DELETE_DELIVERABLE: 'db:delete-deliverable',

  // Document Link operations
  DB_CREATE_DOCUMENT_LINK: 'db:create-document-link',
  DB_GET_DOCUMENT_LINKS: 'db:get-document-links',
  DB_GET_BACKLINKS: 'db:get-backlinks',
  DB_GET_OUTLINKS: 'db:get-outlinks',
  DB_UPDATE_DOCUMENT_LINK: 'db:update-document-link',
  DB_DELETE_DOCUMENT_LINK: 'db:delete-document-link',
  DB_DELETE_DOCUMENT_LINKS: 'db:delete-document-links',
  DB_UPDATE_DOCUMENT_PATH: 'db:update-document-path',
  DB_MARK_LINKS_INVALID: 'db:mark-links-invalid',
  DB_MARK_LINKS_VALID: 'db:mark-links-valid',
  DB_GET_LINK_STATISTICS: 'db:get-link-statistics',
  DB_SEARCH_LINKS: 'db:search-links',
  DB_REPLACE_DOCUMENT_LINKS: 'db:replace-document-links',

  // File system operations
  FS_INITIALIZE: 'fs:initialize',
  FS_REINITIALIZE: 'fs:reinitialize',
  FS_READ_FILE: 'fs:read-file',
  FS_WRITE_FILE: 'fs:write-file',
  FS_DELETE_FILE: 'fs:delete-file',
  FS_MOVE_FILE: 'fs:move-file',
  FS_COPY_FILE: 'fs:copy-file',
  FS_FILE_EXISTS: 'fs:file-exists',
  FS_GET_FILE_INFO: 'fs:get-file-info',
  FS_LIST_DIRECTORY: 'fs:list-directory',
  FS_CREATE_DIRECTORY: 'fs:create-directory',
  FS_RENAME: 'fs:rename',
  FS_DELETE_DIRECTORY: 'fs:delete-directory',
  FS_WATCH_DIRECTORY: 'fs:watch-directory',
  FS_UNWATCH_DIRECTORY: 'fs:unwatch-directory',

  // File system events (from main to renderer)
  FS_EVENT: 'fs:event',
  FS_FILE_CHANGED: 'fs:file-changed',
  FS_FILE_CREATED: 'fs:file-created',
  FS_FILE_DELETED: 'fs:file-deleted',

  // Application operations
  APP_GET_VERSION: 'app:get-version',
  APP_GET_PATH: 'app:get-path',
  APP_SHOW_MESSAGE_BOX: 'app:show-message-box',
  APP_SHOW_ERROR_BOX: 'app:show-error-box',
  APP_SHOW_OPEN_DIALOG: 'app:show-open-dialog',
  APP_SHOW_SAVE_DIALOG: 'app:show-save-dialog',

  // Window operations
  WINDOW_MINIMIZE: 'window:minimize',
  WINDOW_MAXIMIZE: 'window:maximize',
  WINDOW_CLOSE: 'window:close',
  WINDOW_TOGGLE_DEVTOOLS: 'window:toggle-devtools'
} as const

// Database IPC types
export interface DatabaseIpcRequest {
  channel: string
  data?: any
}

export interface DatabaseIpcResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

// File system IPC types
export interface FileSystemIpcRequest {
  channel: string
  path?: string
  content?: string
  options?: any
}

export interface FileSystemIpcResponse<T = any> extends FileOperationResult<T> {}

// Application IPC types
export interface AppIpcRequest {
  channel: string
  data?: any
}

export interface AppIpcResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

// Specific operation types
export interface CreateProjectRequest {
  name: string
  description?: string
  goal?: string
  areaId?: string
}

export interface UpdateProjectRequest {
  id: string
  updates: {
    name?: string
    description?: string
    goal?: string
    status?: string
    progress?: number
    startDate?: Date
    deadline?: Date
    archived?: boolean
  }
}

export interface CreateAreaRequest {
  name: string
  description?: string
  color?: string
  icon?: string
  // {{ AURA-X: Add - 支持完整的Area字段. Approval: 寸止(ID:1738157400). }}
  standard?: string
  status?: string
  reviewFrequency?: string
  archived?: boolean
}

export interface CreateTaskRequest {
  content: string
  description?: string
  priority?: string
  deadline?: Date
  projectId?: string
  areaId?: string
  parentId?: string
  // 资源关联字段
  sourceResourceId?: string
  sourceText?: string
  sourceContext?: string
}

export interface UpdateTaskRequest {
  id: string
  updates: {
    content?: string
    description?: string
    completed?: boolean
    priority?: string
    deadline?: Date
    // Enhanced task attributes
    estimatedHours?: number
    actualHours?: number
    startedAt?: Date
    completedAt?: Date
    status?: string
    progress?: number
    blockedBy?: string
    dependencies?: string[]
    parentId?: string
    projectId?: string
    areaId?: string
    deliverableId?: string
  }
}

export interface CreateResourceRequest {
  resourcePath: string
  title?: string
  projectId?: string
  areaId?: string
}

export interface CreateProjectKPIRequest {
  projectId: string
  name: string
  value: string
  target?: string
  unit?: string
}

export interface UpdateProjectKPIRequest {
  id: string
  updates: {
    name?: string
    value?: string
    target?: string
    unit?: string
  }
}

export interface CreateDeliverableRequest {
  projectId: string
  title: string
  description?: string
  type?: string // document, milestone, metric, artifact
  status?: string // planned, in_progress, completed, delivered, accepted
  priority?: string // low, medium, high, critical
  plannedDate?: Date
  deadline?: Date
  content?: string
  attachments?: any
  metrics?: any
}

export interface UpdateDeliverableRequest {
  id: string
  updates: {
    title?: string
    description?: string
    type?: string
    status?: string
    priority?: string
    plannedDate?: Date
    actualDate?: Date
    deadline?: Date
    content?: string
    attachments?: any
    metrics?: any
  }
}

export interface LinkResourceToProjectRequest {
  resourceId: string
  projectId: string
}

// Document Link request types
export interface CreateDocumentLinkRequest {
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType?: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  linkStrength?: number
}

export interface UpdateDocumentLinkRequest {
  id: string
  sourceDocTitle?: string
  targetDocTitle?: string
  linkText?: string
  displayText?: string
  startPosition?: number
  endPosition?: number
  lineNumber?: number
  columnNumber?: number
  contextBefore?: string
  contextAfter?: string
  isValid?: boolean
  linkStrength?: number
}

export interface DocumentLinkFilters {
  sourceDocPath?: string
  targetDocPath?: string
  linkType?: string
  isValid?: boolean
}

export interface UpdateDocumentPathRequest {
  oldPath: string
  newPath: string
  newTitle?: string
}

export interface SearchLinksRequest {
  query: string
  filters?: DocumentLinkFilters
}

export interface ReplaceDocumentLinksRequest {
  sourceDocPath: string
  links: CreateDocumentLinkRequest[]
}

export interface ReadFileRequest {
  path: string
  encoding?: BufferEncoding
}

export interface WriteFileRequest {
  path: string
  content: string | number[]
  encoding?: BufferEncoding | 'binary'
  createDirs?: boolean
  backup?: boolean
}

export interface MoveFileRequest {
  sourcePath: string
  targetPath: string
  overwrite?: boolean
  createDirs?: boolean
}

export interface CopyFileRequest {
  sourcePath: string
  targetPath: string
  overwrite?: boolean
  createDirs?: boolean
}

// Event types
export interface IpcEventData {
  type: string
  payload: any
  timestamp: Date
}

// API method signatures for type safety
export interface DatabaseApi {
  initialize(): Promise<DatabaseResult<DatabaseConfig>>
  testConnection(): Promise<DatabaseResult<boolean>>
  createProject(data: CreateProjectRequest): Promise<DatabaseResult<any>>
  getProjects(): Promise<DatabaseResult<any[]>>
  updateProject(data: UpdateProjectRequest): Promise<DatabaseResult<any>>
  deleteProject(id: string): Promise<DatabaseResult<void>>
  createArea(data: CreateAreaRequest): Promise<DatabaseResult<any>>
  getAreas(): Promise<DatabaseResult<any[]>>
  createTask(data: CreateTaskRequest): Promise<DatabaseResult<any>>
  getTasks(filters?: any): Promise<DatabaseResult<any[]>>
  updateTask(data: UpdateTaskRequest): Promise<DatabaseResult<any>>
  // ProjectKPI operations
  createProjectKPI(data: CreateProjectKPIRequest): Promise<DatabaseResult<any>>
  getProjectKPIs(projectId: string): Promise<DatabaseResult<any[]>>
  updateProjectKPI(data: UpdateProjectKPIRequest): Promise<DatabaseResult<any>>
  deleteProjectKPI(id: string): Promise<DatabaseResult<void>>
  // Deliverable operations
  createDeliverable(data: CreateDeliverableRequest): Promise<DatabaseResult<any>>
  getProjectDeliverables(projectId: string): Promise<DatabaseResult<any[]>>
  updateDeliverable(data: UpdateDeliverableRequest): Promise<DatabaseResult<any>>
  deleteDeliverable(id: string): Promise<DatabaseResult<void>>
  // Resource linking operations
  getProjectResources(projectId: string): Promise<DatabaseResult<any[]>>
  linkResourceToProject(data: LinkResourceToProjectRequest): Promise<DatabaseResult<any>>
  unlinkResourceFromProject(resourceId: string, projectId: string): Promise<DatabaseResult<void>>
  getAreaResources(areaId: string): Promise<DatabaseResult<any[]>>
  unlinkResourceFromArea(resourceId: string, areaId: string): Promise<DatabaseResult<void>>
  createResource(data: CreateResourceRequest): Promise<DatabaseResult<any>>
  getResources(filters?: any): Promise<DatabaseResult<any[]>>

  // Document Link operations
  createDocumentLink(data: CreateDocumentLinkRequest): Promise<DatabaseResult<any>>
  getDocumentLinks(docPath: string): Promise<DatabaseResult<any>>
  getBacklinks(docPath: string): Promise<DatabaseResult<any[]>>
  getOutlinks(docPath: string): Promise<DatabaseResult<any[]>>
  updateDocumentLink(data: UpdateDocumentLinkRequest): Promise<DatabaseResult<any>>
  deleteDocumentLink(id: string): Promise<DatabaseResult<void>>
  deleteDocumentLinks(docPath: string): Promise<DatabaseResult<void>>
  updateDocumentPath(data: UpdateDocumentPathRequest): Promise<DatabaseResult<void>>
  markLinksAsInvalid(targetDocPath: string): Promise<DatabaseResult<void>>
  markLinksAsValid(targetDocPath: string): Promise<DatabaseResult<void>>
  getLinkStatistics(docPath: string): Promise<DatabaseResult<any>>
  searchLinks(data: SearchLinksRequest): Promise<DatabaseResult<any[]>>
  replaceDocumentLinks(data: ReplaceDocumentLinksRequest): Promise<DatabaseResult<any[]>>
}

export interface FileSystemApi {
  initialize(): Promise<FileOperationResult<FileSystemConfig>>
  reinitialize(workspaceDirectory: string): Promise<FileOperationResult<FileSystemConfig>>
  readFile(data: ReadFileRequest): Promise<FileOperationResult<FileContent>>
  writeFile(data: WriteFileRequest): Promise<FileOperationResult<void>>
  deleteFile(path: string): Promise<FileOperationResult<void>>
  moveFile(data: MoveFileRequest): Promise<FileOperationResult<void>>
  copyFile(data: CopyFileRequest): Promise<FileOperationResult<void>>
  fileExists(path: string): Promise<FileOperationResult<boolean>>
  getFileInfo(path: string): Promise<FileOperationResult<FileInfo>>
  listDirectory(path: string): Promise<FileOperationResult<FileInfo[]>>
  createDirectory(path: string): Promise<FileOperationResult<void>>
  deleteDirectory(path: string): Promise<FileOperationResult<void>>
  rename(oldPath: string, newPath: string): Promise<FileOperationResult<void>>
  watchDirectory(path: string): Promise<FileOperationResult<void>>
  unwatchDirectory(path: string): Promise<FileOperationResult<void>>
}

export interface AppApi {
  getVersion(): Promise<string>
  getPath(name: string): Promise<string>
  showMessageBox(options: any): Promise<any>
  showErrorBox(title: string, content: string): Promise<void>
  showOpenDialog(options: any): Promise<any>
  showSaveDialog(options: any): Promise<any>
}

export interface WindowApi {
  minimize(): Promise<void>
  maximize(): Promise<void>
  close(): Promise<void>
  toggleDevTools(): Promise<void>
}

// Combined API interface
export interface ElectronApi {
  database: DatabaseApi
  fileSystem: FileSystemApi
  app: AppApi
  window: WindowApi

  // Event listeners
  onFileSystemEvent(callback: (event: FileSystemEvent) => void): () => void
  onDatabaseEvent(callback: (event: any) => void): () => void
}

// Global type declaration for window.electronAPI
declare global {
  interface Window {
    electronAPI: ElectronApi
  }
}
