// Shared types between main and renderer processes

export interface DatabaseConfig {
  databasePath: string
  userDataPath: string
}

export interface DatabaseResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

// Re-export Prisma types for convenience
export type {
  User,
  Project,
  ProjectKPI,
  Area,
  AreaMetric,
  Habit,
  HabitRecord,
  Checklist,
  ChecklistInstance,
  Task,
  Tag,
  TaskTag,
  ResourceLink,
  Review,
  InboxNote,
  DocumentLink,
  Deliverable,
  DeliverableResource
} from '@prisma/client'
