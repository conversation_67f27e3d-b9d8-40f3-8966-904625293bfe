-- CreateTable
CREATE TABLE "KPIRecord" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "value" TEXT NOT NULL,
    "note" TEXT,
    "recordedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "kpiId" TEXT NOT NULL,
    CONSTRAINT "KPIRecord_kpiId_fkey" FOREIGN KEY ("kpiId") REFERENCES "ProjectKPI" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE INDEX "KPIRecord_kpiId_recordedAt_idx" ON "KPIRecord"("kpiId", "recordedAt");

-- Add frequency column to ProjectKPI table
ALTER TABLE "ProjectKPI" ADD COLUMN "frequency" TEXT;
